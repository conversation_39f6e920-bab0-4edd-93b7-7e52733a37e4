from fastapi import APIRouter, Depends, HTTPException
from typing import List
import asyncio

from app.models import (
    GenerateTagsRequest,
    ArticleResult,
    TagsResult,
    HealthResponse,
    ErrorResponse
)
from app.core import labels_generator_llm_st, labels_generator_textrank_st

# Dependency functions will be defined here to avoid circular imports
def get_embeddings_manager():
    """Dependency to get embeddings manager."""
    from main import app_state
    return app_state["embeddings_manager"]


def get_nlp_spacy():
    """Dependency to get spaCy NLP model."""
    from main import app_state
    return app_state["nlp_spacy"]


def get_label_pool_data():
    """Dependency to get label pool data."""
    from main import app_state
    return app_state["label_pool_data"]


def get_label_pool_name_set():
    """Dependency to get label pool name set."""
    from main import app_state
    return app_state["label_pool_name_set"]

router = APIRouter()

@router.post("/generate_tags", response_model=List[ArticleResult])
async def generate_tags(
    request: GenerateTagsRequest,
    embeddings_manager=Depends(get_embeddings_manager),
    nlp_spacy=Depends(get_nlp_spacy),
    label_pool_name_set=Depends(get_label_pool_name_set)
):
    """Generate tags for academic articles using NLP and ML models."""

    if not request.items:
        raise HTTPException(status_code=400, detail="Invalid input. 'items' key is required.")

    model = request.model
    pipeline = 1 if model == "pytextrank" else 2

    results = []
    for item in request.items:
        index = item.index or 0
        item_id = item.key or ""
        title = item.title or ""
        abstract = item.abstract or ""

        # Run the label generation in a thread pool since it's CPU-bound
        if pipeline == 1:
            search_result = await asyncio.to_thread(
                labels_generator_textrank_st,
                embeddings_manager,
                nlp_spacy,
                title,
                abstract,
                collection_name="default_collection",
                debug=True
            )
        elif pipeline == 2:
            search_result = await asyncio.to_thread(
                labels_generator_llm_st,
                embeddings_manager,
                label_pool_name_set,
                title,
                abstract,
                collection_name="default_collection",
                llm_model_name=model,
                debug=True
            )
        else:
            search_result = {}

        # Create the result using Pydantic models
        article_result = ArticleResult(
            index=index,
            key=item_id,
            title=title,
            tags=TagsResult(
                matched_tags=search_result.get("matched_labels", []) if search_result.get("matched_labels") else [],
                concept_tags=search_result.get("keywords", []) if search_result.get("keywords") else [],
                person_org_tags=search_result.get("persons_organizations", []) if search_result.get("persons_organizations") else [],
                time_place_tags=search_result.get("times_places", []) if search_result.get("times_places") else []
            )
        )
        results.append(article_result)

    return results

@router.post("/generate_tags_test", response_model=List[ArticleResult])
async def test_tags(request: GenerateTagsRequest):
    """Test endpoint that returns mock tags for development and testing purposes."""

    if not request.items:
        raise HTTPException(status_code=400, detail="Invalid input. 'items' key is required.")

    results = []
    for item in request.items:
        index = item.index or ""
        item_id = item.key or ""
        title = item.title or ""
        abstract = item.abstract or ""

        # Create mock result using Pydantic models
        article_result = ArticleResult(
            index=index,
            key=item_id,
            title=title,
            tags=TagsResult(
                matched_tags=["example_tag1", "example_tag2"],
                concept_tags=["concept_example1", "concept_example2"],
                person_org_tags=["person_example1", "organization_example1"],
                time_place_tags=["time_example1", "place_example1", "Localism"]
            )
        )
        results.append(article_result)

    return results


@router.get("/tags/all")
async def get_all_tags(label_pool_data=Depends(get_label_pool_data)):
    """Retrieve all labels currently available in the label pool."""
    try:
        return label_pool_data
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while fetching tags. Details: {str(e)}"
        )


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Simple health check endpoint to confirm that the API is running."""
    return HealthResponse(status="API is running")