from pydantic import BaseModel, Field
from typing import List, Optional


class ArticleItem(BaseModel):
    """Model for individual article item in request."""
    index: Optional[int] = Field(default=0, description="Article index")
    key: Optional[str] = Field(default="", description="Article key/ID")
    title: Optional[str] = Field(default="", description="Article title")
    abstract: Optional[str] = Field(default="", description="Article abstract")


class GenerateTagsRequest(BaseModel):
    """Request model for generate_tags endpoint."""
    items: List[ArticleItem] = Field(..., description="List of articles to process")
    model: Optional[str] = Field(default="gpt-4o-mini", description="Model to use for processing")


class TagsResult(BaseModel):
    """Model for tags result."""
    matched_tags: List[str] = Field(default_factory=list, description="Matched tags from label pool")
    concept_tags: List[str] = Field(default_factory=list, description="Concept/keyword tags")
    person_org_tags: List[str] = Field(default_factory=list, description="Person and organization tags")
    time_place_tags: List[str] = Field(default_factory=list, description="Time and place tags")


class ArticleResult(BaseModel):
    """Model for individual article result."""
    index: int = Field(..., description="Article index")
    key: str = Field(..., description="Article key/ID")
    title: str = Field(..., description="Article title")
    tags: TagsResult = Field(..., description="Generated tags")


class HealthResponse(BaseModel):
    """Response model for health check endpoint."""
    status: str = Field(..., description="API status")


class ErrorResponse(BaseModel):
    """Response model for errors."""
    error: str = Field(..., description="Error message")
    details: Optional[str] = Field(None, description="Error details")


# For responses that are just lists, we can use List[Model] directly in FastAPI
# No need for RootModel in this case since FastAPI handles it automatically
