# Gunicorn config variables for FastAPI
wsgi_app = "main:app"
bind = "0.0.0.0:5011"  # Bind to all IPs on this port
workers = 2  # worker processes (adjust based on your server's CPU cores)
worker_class="uvicorn.workers.UvicornWorker"  # Use uvicorn worker class for FastAPI
timeout = 60  # Shorter timeout for requests (adjust based on your app's response time)
keepalive = 2  # Shorter keepalive for connections
max_requests = 1000  # Restart workers after 500 requests to prevent memory leaks
max_requests_jitter = 50  # Add some randomness to max_requests to avoid all workers restarting at once
reload = False  # Disable auto-reload (set to True during development if needed)
preload = True  # Preload application code for better performance
accesslog = "-"  # Log access to stdout
errorlog = "-"  # Log errors to stdout
loglevel = "info"  # Log level